body {
  overflow: inherit !important;
}

.login-bg {
  background-image: url("../img/login-banner.png");
  background-repeat: no-repeat;
  background-position: 50%;
}

.mat-menu-panel {
  min-width: 400px !important;
  max-width: 464px !important;
}

.w-10 {
  min-width: 30% !important;
}

.w-70 {
  width: 50% !important;
}

.country_filter{
  margin: 0em;
  padding: 0.4em;
  position: sticky !important;
  top: 0;
  z-index: 1;
}

.image-upload {
  position: relative;
  display: flex;
  justify-content: center;
}

.image-upload .add-photo-btn {
  position: absolute;
  top: 0;
  display: none;
}

.image-upload:hover .add-photo-btn {
  display: block;
}

.image-upload #file-input {
  display: none;
}

.add_company .jumbotron {
  height: 50vh;
  align-items: flex-start;
  width: 100%;
}

.company-second-part {
  width: 100%;
  background: white;
}

.add_company .view-container {
  display: flex;
  width: 100%;
  justify-content: center;
}

.add_company form {
  position: absolute;
  top: 20%;
  margin-bottom: 80px;
}

.authorization {
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(10, 148, 188, 0.15);
}

.authorization .--wrapper {
  width: 40%;
  height: 80%;
  box-shadow: 4px 6px 10px 0px rgba(0, 0, 0, 0.5);
  padding: 2%;
  border-radius: 4px;
  background: #fff;
  overflow: hidden;
}

.--wrapper h2 {
  color: #000000d9;
}

.--wrapper p {
  font-size: 18px;
  margin-top: 35px;
  margin-bottom: 25px;
  color: #000000e0;
}

.--wrapper .point {
  padding: 10px 0 10px 15px;
  border-top: 1px solid #21252936;
  margin: 0;
}

.--wrapper .point i {
  color: #2a73e8c4;
  margin-right: 16px;
  font-size: 21px;
}

.--wrapper .point > div {
  font-size: 13.5px;
  color: #000000f0;
  display: flex;
  align-items: center;
}

.--wrapper h2 {
  margin-top: 10px;
}
.point.last {
  display: flex;
  justify-content: flex-end;
  padding-top: 25px;
}
.point.last button {
  margin-left: 15px;
  width: 90px;
  padding: 5px 10px;
  font-weight: 600;
  font-size: 13px;
  border-radius: 2px;
}

.point.last .authorize {
  background: #2a73e8;
  color: #fff;
  border: 0;
}

.point.last .cancel {
  background: rgba(0, 0, 0, 0.08);
  border: 1px solid #34343452;
  color: #000000b0;
}

.down-nav {
  margin-top: 10%;
}

.pad-5 {
  padding: 5%;
}

.custom-button-width {
  width: 90px;
}

.backIcon {
  height: 80px;
}

/*---------------------------------Questionnaire-------------------------------*/
.questionnaire-wrap {
  height: 100%;
  background: rgba(32, 164, 100, 0.15);
  color: #343434;
  overflow-y: auto;
  align-items: center;
}
.questionnaire-wrap .container {
  height: 100%;
  display: block;
  justify-content: center;
  align-items: center;
}
.questionnaire-card {
  overflow: auto !important;
  position: relative;
  border-radius: .5rem;
  background: #fff;
  -webkit-box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
  height: 95vh;
}
.questionnaire-text {
  font-weight: 600;
  font-size: 17px;
}
.teaxtare-location-icon.input-group-text {
  padding: 1.65rem 1rem !important;
}
.setup-img {
  width: 100px;
}
.questionnaire-card .card-title {
  border-bottom: 1px solid rgba(52, 52, 52, 0.1);
  padding-bottom: 1rem;
  margin: 1rem 1rem 1rem;
  position: relative;
  z-index: 1;
  font-size: 1.6rem;
  text-transform: uppercase;
  font-weight: 300;
  line-height: 1.2;
}
.questionnaire-card .card-title:after {
  content: "";
  background-color: #0a94bc;
  width: 32px;
  height: 5px;
  border-radius: 2px;
  display: block;
  position: absolute;
  bottom: -3px;
  left: 0px;
  right: 0px;
  margin: auto;
  padding-top: 0 !important;
}

.agm-styles {
  width: 100%;
  height: 300px;
}

.min-90vh {
  min-height: 80vh;
}
.questionary-end {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}
@media screen and (max-width: 767px) {
  .questionnaire-wrap {
    font-size: 13px;
    background: #fff;
  }
  .questionnaire-wrap .container {
    padding: 0;
    display: block;
  }
  .questionnaire-box {
    padding: 0;
  }
  .questionnaire-card {
      margin: 0 !important;
      border-radius: 0;
      box-shadow: none !important;
      height: 100vh;
  }
  .questionnaire-card .card-title {
    font-size: 1.2rem;
  }
  .questionnaire-text {
    font-weight: 600;
    font-size: 15px;
  }
  .createAccount button,
  .changeUsername button{
    padding: 0 7px;
            font-size: 12px;
  }
}

/*---------------------------------/Questionnaire-------------------------------*/

.create-new-account {
  padding: 20px;
}

.red-logo {
  /* margin: 100px 0 25px 0; */
  width: 100px;
}

.add_company {
  overflow-y: auto !important;
  height: 100vh !important;
}

.add_company .view-container {
  position: relative !important;
  height: 100% !important;
  overflow-y: auto !important;
}
.oauth-loader {
  height: 100vh;
  width: 100vw;
  display: flex;
  align-items: center;
  justify-content: center;
}
.hide-ui-loader {
  height: 100vh;
  width: 100vw;
  display: flex;
  align-items: center;
  justify-content: center;
}

.h-90vh [mat-dialog-title] {
  background: #dee2e6;
  padding: 1rem 1.5rem;
  margin-bottom: 0 !important;
  font: 500 20px/32px Roboto, "Helvetica Neue", sans-serif;
}
.modal-content-custom {
  width: 100%;
  padding: 2% 5%;
}

.modal-content-custom .success-big {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 130px;
}
.modal-content-custom .success-big i {
  color: #28a745;
  font-size: 100px;
}
.modal-bottom-custom {
  position: sticky;
  bottom: 0;
  z-index: 1020;
  text-align: right;
  padding: 1rem 1.5rem;
  border-top: 1px solid #dee2e6;
}

.fas.fa-phone {
  transform: rotate(100deg);
}

.backdropBackground {
  background-color: rgba(0, 0, 0, 0.8);
}

.add-multichain-name-add {
  width: 100%;
  display: flex !important;
  align-items: center;
  justify-content: center;
  height: 185.875px;
}

.add-multichain-name-add i {
  font-size: 30px;
  color: #0a94bc;
  cursor: pointer;
}

.info-company p {
  margin-bottom: 0;
}
.d-md-flex {
  display: flex !important;
}

.invisible-control {
  height: 0;
  overflow: hidden;
  width: 0;
}
.mat-drawer-container {
  height: 100%;
}

/*----------------------------------Notification panel--------------------------------- */
.notification-panel {
  background: #fff;
  border: 1px solid #ddd;
  position: absolute;
  top: 64px;
  z-index: 99;
  width: 400px;
  right: 0;
  height: calc(100% - 64px);
  transition: right 0.2s ease-in-out;
}
.notification-panel .mat-tab-body {
  font-weight: normal;
  font-size: 14px;
}
.notification-panel:before {
  content: "";
  border-style: solid;
  border-width: 0 11px 11px;
  width: 1px;
  z-index: 1;
  left: 46.5%;
  position: absolute;
  display: block;
  border-color: #dcdcdc #bbb7b700;
  top: -12px;
}
.notification-panel .notification .mat-card:hover {
  background: rgba(0, 0, 0, 0.04);
}
.notification-panel .notification .mat-card {
  box-shadow: none;
  padding: 15px;
  border-bottom: 1px solid #ddd;
}
.notification-panel .notification .mat-card-title {
  line-height: 1.5em;
  margin-bottom: 2px;
  display: flex;
  justify-content: space-between;
}
.notification-panel .notification .mat-card-subtitle {
  line-height: 1.5em;
  font-size: 12px;
}
.notification-panel .notification .mat-card-header-text {
  margin: 0 8px;
  width: 320px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.notification-panel .mat-tab-header {
  margin: 10px 10px 0;
}
.notification-panel .mat-tab-group {
  display: block;
}
.notification-panel .mat-tab-body-content {
  padding-top: 0 !important;
}
.bell-badge .badge {
  border-radius: 50%;
  line-height: initial;
  padding: 3px 6px;
  left: -7px;
  top: -8px;
  padding: 3px 6px;
  left: 34px;
  top: 18px;
  position: absolute;
  text-align: center;
  display: inline-block;
}
.notification-date-time {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
button.accept-link {
  color: #20a464;
}
.notification-panel .mat-tab-body-wrapper {
  box-shadow: -2px 5px 10px 1px rgba(0, 0, 0, 0.176);
  height: 1000px;
  overflow-y: auto;
  background: #fff;
}

.notification-panel .mat-tab-body-wrapper .mat-tab-body {
  overflow-y: auto !important;
}
/*----------------------------------/Notification panel--------------------------------- */

.questionnaire-footer{
  padding: 10px;
  background: #fff;
  position: sticky;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 999;
  border-top: 1px solid rgba(0, 0, 0, 0.15);
}
.questionnaire-foot-btns{
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mat-grid-drop {
  height: 268px;
}

.error-snack-bar.mat-snack-bar-container {
  color: #fff;
  background: #f44336;
}

.bank-border-margin{
  margin-bottom: 10px;
}

.bank-border-margin-left{
  margin-left: 10px;
}

.md-select-value span:first-child {
    white-space: normal;
}

.mat-icon-button .mat-icon, .mat-icon-button i{
  font-size: 16px !important;
}

.mat-select-value{
  max-width: 0 !important;
}


.logo-height{
  height: 57px;
}